--- Log Start: 2025-08-14 16:06:47 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 663 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 5020 ------------
length dataset['train']: 5020
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([9, 768])
depth 2: torch.Size([45, 768])
depth 3: torch.Size([119, 768])
depth 4: torch.Size([554, 768])
depth 5: torch.Size([682, 768])
depth 6: torch.Size([695, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 9, 45, 119, 554, 682, 695]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 663 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 328
   - 总知识点关联数: 663
🚀 开始批量预测...
🔄 开始批量预测 328 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/328 条题目...
   已分析 100/328 条题目...
   已分析 150/328 条题目...
   已分析 200/328 条题目...
   已分析 250/328 条题目...
   已分析 300/328 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 328
   - 成功预测数量: 328
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 87.71%
   - 完全匹配题目: 256
   - 部分匹配题目: 56
   - 无匹配题目: 16

📚 知识点数量分析:
   - 平均知识点数量: 2.02
   - 最多知识点数量: 8
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 142 题）：
   - 命中 1 个知识点: 138题，占比 97.18%
   - 未命中任何知识点: 4题，占比 2.82%

🧩 题目含 2 个知识点（共 97 题）：
   - 命中 1 个知识点: 9题，占比 9.28%
   - 命中 2 个知识点: 81题，占比 83.51%
   - 未命中任何知识点: 7题，占比 7.22%

🧩 题目含 3 个知识点（共 50 题）：
   - 命中 1 个知识点: 7题，占比 14.00%
   - 命中 2 个知识点: 4题，占比 8.00%
   - 命中 3 个知识点: 36题，占比 72.00%
   - 未命中任何知识点: 3题，占比 6.00%

🧩 题目含 4 个知识点（共 26 题）：
   - 命中 1 个知识点: 2题，占比 7.69%
   - 命中 2 个知识点: 3题，占比 11.54%
   - 命中 3 个知识点: 18题，占比 69.23%
   - 命中 4 个知识点: 1题，占比 3.85%
   - 未命中任何知识点: 2题，占比 7.69%

🧩 题目含 5 个知识点（共 9 题）：
   - 命中 1 个知识点: 2题，占比 22.22%
   - 命中 2 个知识点: 2题，占比 22.22%
   - 命中 3 个知识点: 5题，占比 55.56%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%

🧩 题目含 6 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 2题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 2题，占比 100.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-08-18 14:04:05 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 663 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 5020 ------------
length dataset['train']: 5020
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([9, 768])
depth 2: torch.Size([45, 768])
depth 3: torch.Size([119, 768])
depth 4: torch.Size([554, 768])
depth 5: torch.Size([682, 768])
depth 6: torch.Size([695, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 9, 45, 119, 554, 682, 695]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 663 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 328
   - 总知识点关联数: 663
🚀 开始批量预测...
🔄 开始批量预测 328 条文本（批大小: 256）...
/home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/328 条题目...
   已分析 100/328 条题目...
   已分析 150/328 条题目...
   已分析 200/328 条题目...
   已分析 250/328 条题目...
   已分析 300/328 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 328
   - 成功预测数量: 328
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 95.21%
   - 完全匹配题目: 284
   - 部分匹配题目: 42
   - 无匹配题目: 2

📚 知识点数量分析:
   - 平均知识点数量: 2.02
   - 最多知识点数量: 8
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 142 题）：
   - 命中 1 个知识点: 141题，占比 99.30%
   - 未命中任何知识点: 1题，占比 0.70%

🧩 题目含 2 个知识点（共 97 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 96题，占比 98.97%
   - 未命中任何知识点: 1题，占比 1.03%

🧩 题目含 3 个知识点（共 50 题）：
   - 命中 1 个知识点: 1题，占比 2.00%
   - 命中 2 个知识点: 3题，占比 6.00%
   - 命中 3 个知识点: 46题，占比 92.00%

🧩 题目含 4 个知识点（共 26 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 25题，占比 96.15%
   - 命中 4 个知识点: 1题，占比 3.85%

🧩 题目含 5 个知识点（共 9 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 1题，占比 11.11%
   - 命中 3 个知识点: 8题，占比 88.89%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%

🧩 题目含 6 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 2题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 1题，占比 50.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 1题，占比 50.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_physics2_VIT/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
